<script setup lang="ts">
import { useToast } from "primevue/usetoast";
import { onMounted, ref, watch } from "vue";
import { usePermission } from "../../composables/usePermission";
import {
  getCustomersSimpleList,
  createAccountSeq,
} from "../../services/customer";
import type { AccountSeqInfo } from "../../types/customer";
import { formatDateTime } from "../../utils/common";

const accountSeqs = ref<AccountSeqInfo[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

const selectedCustomerNum = ref<string>("");

// 筛选相关
const accountSeqFilter = ref("");
const seqNameFilter = ref("");

// 加载客户简单列表
const customerOptions = ref<{ label: string; value: string }[]>([]);
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    customerOptions.value = response.data.map((item) => ({
      label: `${item.customer_name} (${item.customer_num})`,
      value: item.customer_num,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 加载分账序号列表数据
const loadAccountSeqs = async () => {
  try {
    loading.value = true;
    const params: any = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    // 使用选中的客户编号进行筛选
    if (selectedCustomerNum.value) {
      params.customer_num = selectedCustomerNum.value;
    }
    if (accountSeqFilter.value) {
      params.account_seq = accountSeqFilter.value;
    }
    if (seqNameFilter.value) {
      params.seq_name = seqNameFilter.value;
    }

    const response = await fetch(
      "/api/account-seq?" + new URLSearchParams(params)
    );
    const data = await response.json();

    accountSeqs.value = data.data.records;
    totalRecords.value = data.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载分账序号列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadAccountSeqs();
};

// 监听客户编号变化，重新加载分账序号列表
watch(selectedCustomerNum, () => {
  lazyParams.value.page = 1; // 重置到第一页
  loadAccountSeqs();
});

onMounted(async () => {
  await initializeUserInfo();
  loadCustomerOptions();
  loadAccountSeqs();
});

// 表单相关
const accountSeqDrawerVisible = ref(false);
const accountSeqForm = ref({
  customer_id: 0,
  seq_name: "",
  tax: 0,
});

// 税率选项
const taxOptions = [
  { label: "0%", value: 0 },
  { label: "6%", value: 6 },
  { label: "9%", value: 9 },
  { label: "13%", value: 13 },
];

// 字段错误信息
const fieldErrors = ref<{ [key: string]: string }>({});

// 打开新建对话框
const openNew = () => {
  accountSeqForm.value = {
    customer_id: 0,
    seq_name: "",
    tax: 0,
  };
  accountSeqDrawerVisible.value = true;
  fieldErrors.value = {};
};

// 保存分账序号信息
const saveAccountSeq = async () => {
  try {
    const response = await createAccountSeq(accountSeqForm.value);

    if (response.ok) {
      accountSeqDrawerVisible.value = false;
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "分账序号创建成功",
        life: 3000,
      });
      loadAccountSeqs();
    } else {
      throw new Error("创建失败");
    }
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "分账序号创建失败",
      life: 3000,
    });
  }
};
</script>

<template>
  <div class="account-seq-container">
    <Toast />
    <div class="card">
      <Toolbar class="mb-2">
        <template #start>
          <Select
            v-model="selectedCustomerNum"
            :options="customerOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="请选择客户"
            filter
            class="w-70"
          />
        </template>
        <template #end>
          <div class="filter-group">
            <FloatLabel class="mr-2">
              <InputText id="accountSeqFilter" v-model="accountSeqFilter" />
              <label for="accountSeqFilter">分账序号</label>
            </FloatLabel>
            <FloatLabel class="mr-2">
              <InputText id="seqNameFilter" v-model="seqNameFilter" />
              <label for="seqNameFilter">分账序号名称</label>
            </FloatLabel>
            <Button rounded icon="pi pi-search" @click="loadAccountSeqs" />
          </div>
          <Divider layout="vertical" />
          <Button
            label="新建"
            icon="pi pi-plus"
            @click="openNew"
            :disabled="!hasOperationPermission || !selectedCustomerNum"
          />
        </template>
      </Toolbar>

      <!-- 卡片视图 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" />
      </div>

      <div v-else-if="accountSeqs && accountSeqs.length > 0" class="card-grid">
        <div
          v-for="accountSeq in accountSeqs"
          :key="accountSeq.id"
          class="account-seq-card"
        >
          <div class="card-header-section">
            <div class="card-title">
              <Tag
                :value="accountSeq.account_seq"
                severity="info"
                size="large"
              />
            </div>
          </div>
          <div class="card-content">
            <div class="info-row">
              <span class="label">客户名称:</span>
              <span class="value">{{ accountSeq.customer_name }}</span>
            </div>
            <div class="info-row">
              <span class="label">分账序号名称:</span>
              <span class="value">{{ accountSeq.seq_name }}</span>
            </div>
            <div class="info-row">
              <span class="label">税率:</span>
              <span class="value">{{ accountSeq.tax }}%</span>
            </div>
            <div class="info-row">
              <span class="label">创建时间:</span>
              <span class="value">{{
                formatDateTime(accountSeq.created_at)
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-message">
        <i
          class="pi pi-inbox"
          style="
            font-size: 2rem;
            color: var(--p-text-color-secondary);
            margin-bottom: 1rem;
          "
        ></i>
        <p>暂无分账序号数据</p>
      </div>

      <!-- 分页器 -->
      <Paginator
        v-if="accountSeqs && accountSeqs.length > 0"
        :rows="20"
        :totalRecords="totalRecords"
        :rowsPerPageOptions="[10, 20, 50]"
        @page="onPage($event)"
        class="mt-4"
      />
    </div>
  </div>

  <!-- 新建/编辑分账序号抽屉 -->
  <Drawer
    v-model:visible="accountSeqDrawerVisible"
    position="right"
    :style="{ width: '70rem' }"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :header="'新增分账序号'"
    class="account-seq-drawer p-fluid"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="customer_id" class="required">
                  客户
                  <span v-if="fieldErrors.customer_id" class="p-error ml-2">{{
                    fieldErrors.customer_id
                  }}</span>
                </label>
                <Select
                  v-model="accountSeqForm.customer_id"
                  :options="customerOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="请选择客户"
                  filter
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="tax" class="required">
                  税率
                  <span v-if="fieldErrors.tax" class="p-error ml-2">{{
                    fieldErrors.tax
                  }}</span>
                </label>
                <Select
                  v-model="accountSeqForm.tax"
                  :options="taxOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="请选择税率"
                  class="w-full"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 分账序号信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">分账序号信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="seq_name" class="required">
                  分账序号名称
                  <span v-if="fieldErrors.seq_name" class="p-error ml-2">{{
                    fieldErrors.seq_name
                  }}</span>
                </label>
                <InputText
                  v-model="accountSeqForm.seq_name"
                  required
                  class="w-full"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="accountSeqDrawerVisible = false"
        />
        <Button label="保存" icon="pi pi-check" @click="saveAccountSeq" />
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
.account-seq-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
  background: #f8f9fa;
}

.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  height: calc(100vh - 22rem);
  overflow-y: auto;
}

/* 单张卡片样式 */
.account-seq-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
}

.account-seq-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 123, 255, 0.2);
}

.card-header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.card-title h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  line-height: 1.1;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row .label {
  font-weight: 500;
  color: #6e6e73;
  font-size: 0.9rem;
}

.info-row .value {
  font-weight: 400;
  color: #1d1d1f;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem;
}

/* 按钮样式 */
:deep(.p-button) {
  transition: all 0.2s ease;
}

:deep(.p-button:hover) {
  transform: translateY(-1px);
}

/* 表单分段样式 - 参考OrderList.vue */
.form-section {
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--p-surface-card);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin: 0 0 1rem 0;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 分账序号抽屉样式 - 参考OrderList.vue */
:deep(.account-seq-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

:deep(.p-drawer-header) {
  padding: 1rem;
  border-bottom: 1px solid var(--p-surface-border);
}

:deep(.p-drawer-content) {
  padding: 0;
}

:deep(.p-drawer-footer) {
  padding: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.w-full {
  width: 100%;
}

/* 表单验证错误样式 - 参考OrderList.vue */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

/* 分页器样式 */
:deep(.p-paginator) {
  border-radius: 8px;
  border: 1px solid #e5e5e7;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-group {
    flex-direction: column;
    width: 100%;
  }

  .filter-group > * {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .account-seq-container {
    padding: 0.5rem;
  }

  .card {
    padding: 1rem;
    border-radius: 12px;
  }

  .account-seq-card {
    padding: 1rem;
  }
}
</style>
